from ttkbootstrap import <PERSON><PERSON>, Label, Combobox, Frame, PhotoImage
from tkinterdnd2 import DND_FILES, TkinterDnD
from tkinter import filedialog
import os

def create_main_window():
    root = TkinterDnD.Tk()
    root.title("Image Converter")
    root.geometry("400x300")

    frame = Frame(root)
    frame.pack(pady=20)

    label = Label(frame, text="Drag and drop an image file here:")
    label.pack(pady=10)

    drop_area = Frame(frame, width=300, height=100, relief="sunken")
    drop_area.pack(pady=10)
    drop_area.drop_target_register(DND_FILES)
    drop_area.dnd_bind('<<Drop>>', drop)

    conversion_label = Label(frame, text="Select Conversion Type:")
    conversion_label.pack(pady=10)

    conversion_types = ["svg_to_webp", "png_to_ico", "svg_to_ico"]
    conversion_dropdown = Combobox(frame, values=conversion_types)
    conversion_dropdown.pack(pady=10)

    save_button = Button(frame, text="Save As", command=save_as)
    save_button.pack(pady=20)

    root.mainloop()

def drop(event):
    file_path = event.data
    if os.path.isfile(file_path):
        print(f"File dropped: {file_path}")
        # Here you can add logic to handle the dropped file

def save_as():
    file_path = filedialog.asksaveasfilename(defaultextension=".webp",
                                               filetypes=[("WebP files", "*.webp"),
                                                          ("ICO files", "*.ico"),
                                                          ("All files", "*.*")])
    if file_path:
        print(f"Save as: {file_path}")
        # Here you can add logic to save the converted file

create_main_window()