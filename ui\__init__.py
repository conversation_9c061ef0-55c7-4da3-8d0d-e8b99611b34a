"""
UI module for the Image Converter application.
This module provides UI components and utilities.
"""

import ttkbootstrap as ttk
from ttkbootstrap.constants import *


def create_ui(master, image_convert_func=None):
    """
    Create the main UI for the image converter application.

    Args:
        master: The root window
        image_convert_func: The image conversion function (for compatibility)

    Returns:
        ImageConverterApp: The application instance
    """
    # Import here to avoid circular imports
    from converter import ImageConverterApp

    # Create and return the application instance
    app = ImageConverterApp(master)
    return app


# For backward compatibility
def create_main_window():
    """Create a standalone main window (for testing purposes)."""
    from converter import ImageConverterApp

    root = ttk.Window(themename="darkly")
    root.title("Image Converter")
    root.geometry("500x400")
    root.resizable(True, True)

    app = ImageConverterApp(root)
    return root, app


# Only run if this module is executed directly
if __name__ == "__main__":
    root, app = create_main_window()
    root.mainloop()