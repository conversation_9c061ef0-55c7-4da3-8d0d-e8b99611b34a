#!/usr/bin/env python3
"""
Quick GUI test - opens the application for 3 seconds then closes automatically.
This verifies the GUI actually displays correctly.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_gui_test():
    """Open GUI for a few seconds to verify it works."""
    try:
        print("🚀 Starting quick GUI test...")
        
        import ttkbootstrap as ttk
        from converter import ImageConverterApp
        
        # Create window
        root = ttk.Window(themename="darkly")
        root.title("Image Converter - Quick Test")
        root.geometry("500x400")
        
        # Create app
        app = ImageConverterApp(root)
        
        print("✅ GUI opened successfully!")
        print("🔍 Checking UI components...")
        
        # Check that key UI elements exist
        if hasattr(app, 'convert_button'):
            button_text = app.convert_button.cget('text')
            print(f"  ✓ Convert button found: '{button_text}'")
        
        if hasattr(app, 'advanced_button'):
            button_text = app.advanced_button.cget('text')
            print(f"  ✓ Advanced button found: '{button_text}'")
        
        if hasattr(app, 'conversion_type'):
            print("  ✓ Conversion type dropdown found")
        
        print("\n🎉 GUI test completed successfully!")
        print("💡 The application is ready to use.")
        
        # Auto-close after showing success
        root.after(2000, root.destroy)  # Close after 2 seconds
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_gui_test()
    if success:
        print("\n✅ Application is working correctly!")
        print("🚀 Run 'python main.py' to use the Image Converter")
    else:
        print("\n❌ There are issues with the application")
    
    sys.exit(0 if success else 1)
