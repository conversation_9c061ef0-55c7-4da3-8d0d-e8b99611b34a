#!/usr/bin/env python3
"""
Simple launcher script for the Image Converter application.
This script provides a safe way to test the GUI application.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Launch the Image Converter application."""
    try:
        print("Starting Image Converter Application...")
        
        # Import required modules
        import ttkbootstrap as ttk
        from converter import ImageConverterApp
        
        # Create the main window
        root = ttk.Window(themename="darkly")
        root.title("Image Converter")
        root.geometry("500x400")
        root.resizable(True, True)
        
        # Center the window on screen
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f"{width}x{height}+{x}+{y}")
        
        print("Creating application interface...")
        
        # Create the application
        app = ImageConverterApp(root)
        
        print("Application ready! Close the window to exit.")
        
        # Start the GUI event loop
        root.mainloop()
        
        print("Application closed.")
        
    except ImportError as e:
        print(f"Import Error: {e}")
        print("Please install required dependencies:")
        print("pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("Press Enter to exit...")
    sys.exit(0 if success else 1)
