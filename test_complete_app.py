#!/usr/bin/env python3
"""
Complete test script for the Image Converter application.
Tests both the core functionality and GUI components.
"""

import sys
import os
import tempfile
import traceback

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test all required imports."""
    print("🔍 Testing imports...")
    
    try:
        import ttkbootstrap as ttk
        print("  ✓ ttkbootstrap imported")
    except ImportError as e:
        print(f"  ✗ ttkbootstrap failed: {e}")
        return False
    
    try:
        from PIL import Image
        print("  ✓ PIL imported")
    except ImportError as e:
        print(f"  ✗ PIL failed: {e}")
        return False
    
    try:
        from image_convert import image_convert, png_to_ico, SVG_SUPPORT
        print(f"  ✓ image_convert imported (SVG support: {SVG_SUPPORT})")
    except ImportError as e:
        print(f"  ✗ image_convert failed: {e}")
        return False
    
    try:
        from converter import ImageConverterApp
        print("  ✓ ImageConverterApp imported")
    except ImportError as e:
        print(f"  ✗ ImageConverterApp failed: {e}")
        return False
    
    return True

def test_core_functionality():
    """Test core image conversion functionality."""
    print("\n🔧 Testing core functionality...")
    
    try:
        from image_convert import png_to_ico
        from PIL import Image
        
        # Create a test PNG
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_png:
            img = Image.new('RGBA', (32, 32), (0, 255, 0, 255))
            img.save(temp_png.name, 'PNG')
            temp_png_path = temp_png.name
        
        with tempfile.NamedTemporaryFile(suffix='.ico', delete=False) as temp_ico:
            temp_ico_path = temp_ico.name
        
        # Test conversion
        result = png_to_ico(temp_png_path, temp_ico_path)
        
        if result and os.path.exists(temp_ico_path):
            file_size = os.path.getsize(temp_ico_path)
            print(f"  ✓ PNG to ICO conversion successful ({file_size} bytes)")
            success = True
        else:
            print("  ✗ PNG to ICO conversion failed")
            success = False
        
        # Cleanup
        for f in [temp_png_path, temp_ico_path]:
            if os.path.exists(f):
                os.unlink(f)
        
        return success
        
    except Exception as e:
        print(f"  ✗ Core functionality test failed: {e}")
        return False

def test_gui_creation():
    """Test GUI creation without showing it."""
    print("\n🎨 Testing GUI creation...")
    
    try:
        import ttkbootstrap as ttk
        from converter import ImageConverterApp
        
        # Create window (but don't show it)
        root = ttk.Window(themename="darkly")
        root.withdraw()  # Hide the window
        
        # Create app
        app = ImageConverterApp(root)
        
        # Test that key components exist
        if hasattr(app, 'convert_and_save_image'):
            print("  ✓ convert_and_save_image method exists")
        else:
            print("  ✗ convert_and_save_image method missing")
            return False
        
        if hasattr(app, 'input_file'):
            print("  ✓ input_file attribute exists")
        else:
            print("  ✗ input_file attribute missing")
            return False
        
        if hasattr(app, 'conversion_type'):
            print("  ✓ conversion_type attribute exists")
        else:
            print("  ✗ conversion_type attribute missing")
            return False
        
        # Destroy the window
        root.destroy()
        
        print("  ✓ GUI creation successful")
        return True
        
    except Exception as e:
        print(f"  ✗ GUI creation failed: {e}")
        traceback.print_exc()
        return False

def test_main_py():
    """Test that main.py can be imported without errors."""
    print("\n📄 Testing main.py...")
    
    try:
        # Test importing main module
        import main
        print("  ✓ main.py imports successfully")
        
        # Test that main function exists
        if hasattr(main, 'main'):
            print("  ✓ main() function exists")
        else:
            print("  ✗ main() function missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ✗ main.py test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Image Converter - Complete Application Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Core Functionality", test_core_functionality),
        ("GUI Creation", test_gui_creation),
        ("Main Module", test_main_py)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! The application should work correctly.")
        print("\nTo run the application:")
        print("  python main.py")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
