#!/usr/bin/env python3
"""
Test script for the Image Converter application.
This script tests the basic functionality without running the GUI.
"""

import os
import sys
import tempfile
from PIL import Image

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from image_convert import image_convert
    print("✓ Successfully imported image_convert module")
except ImportError as e:
    print(f"✗ Failed to import image_convert: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ Error importing image_convert: {e}")
    sys.exit(1)

def test_png_to_ico():
    """Test PNG to ICO conversion."""
    print("\nTesting PNG to ICO conversion...")
    
    # Create a simple test PNG image
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_png:
        # Create a simple 64x64 red square
        img = Image.new('RGBA', (64, 64), (255, 0, 0, 255))
        img.save(temp_png.name, 'PNG')
        temp_png_path = temp_png.name
    
    # Create output ICO file
    with tempfile.NamedTemporaryFile(suffix='.ico', delete=False) as temp_ico:
        temp_ico_path = temp_ico.name
    
    try:
        # Test the conversion
        result = image_convert(temp_png_path, temp_ico_path, 'png_to_ico')
        
        if result and os.path.exists(temp_ico_path):
            file_size = os.path.getsize(temp_ico_path)
            print(f"✓ PNG to ICO conversion successful! Output file size: {file_size} bytes")
        else:
            print("✗ PNG to ICO conversion failed - no output file created")
            
    except Exception as e:
        print(f"✗ PNG to ICO conversion failed with error: {e}")
    
    finally:
        # Clean up temporary files
        for temp_file in [temp_png_path, temp_ico_path]:
            if os.path.exists(temp_file):
                os.unlink(temp_file)

def test_imports():
    """Test all required imports."""
    print("Testing imports...")
    
    try:
        import ttkbootstrap
        print("✓ ttkbootstrap imported successfully")
    except ImportError:
        print("✗ ttkbootstrap not available - install with: pip install ttkbootstrap")
        return False
    
    try:
        from PIL import Image
        print("✓ Pillow imported successfully")
    except ImportError:
        print("✗ Pillow not available - install with: pip install Pillow")
        return False
    
    try:
        from svglib.svglib import renderSVG
        print("✓ svglib imported successfully")
    except ImportError:
        print("✗ svglib not available - install with: pip install svglib")
        return False
    
    try:
        from reportlab.graphics import renderPM
        print("✓ reportlab imported successfully")
    except ImportError:
        print("✗ reportlab not available - install with: pip install reportlab")
        return False
    
    return True

def main():
    """Run all tests."""
    print("Image Converter Application Test Suite")
    print("=" * 40)
    
    # Test imports first
    if not test_imports():
        print("\n✗ Some required dependencies are missing.")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    # Test basic conversion functionality
    test_png_to_ico()
    
    print("\n" + "=" * 40)
    print("Test suite completed!")
    
    # Test GUI imports
    print("\nTesting GUI components...")
    try:
        from converter import ImageConverterApp
        print("✓ ImageConverterApp imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import ImageConverterApp: {e}")
        return False
    
    try:
        from ui import create_ui
        print("✓ UI module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import UI module: {e}")
        return False
    
    print("\n✓ All tests passed! The application should work correctly.")
    print("Run 'python main.py' to start the GUI application.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
