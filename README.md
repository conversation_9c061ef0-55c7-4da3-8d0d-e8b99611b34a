# ttkbootstrap-image-converter

## Overview
The Ttkbootstrap Image Converter is a Python application that allows users to convert images between different formats using a simple and intuitive graphical user interface (GUI). The application supports various conversion types, including SVG to WebP, PNG to ICO, and SVG to ICO.

## Features
- **File Browsing**: Users can easily select input image files using a file dialog or by dragging and dropping images into the application.
- **Conversion Type Selection**: A dropdown menu allows users to choose the desired conversion type.
- **Save As Option**: Users can specify the output file name and location for the converted image.
- **User-Friendly Interface**: Built with Ttkbootstrap for a modern and responsive design.

## Project Structure
```
ttkbootstrap-image-converter
├── src
│   ├── main.py          # Entry point of the application
│   ├── converter.py     # Contains image conversion logic
│   └── ui
│       └── __init__.py  # Defines user interface components
├── requirements.txt      # Lists project dependencies
└── README.md             # Documentation for the project
```

## Installation
To set up the project, follow these steps:

1. Clone the repository:
   ```
   git clone <repository-url>
   cd ttkbootstrap-image-converter
   ```

2. Create a virtual environment (optional but recommended):
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows use `venv\Scripts\activate`
   ```

3. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage
To run the application, execute the following command:
```
python main.py
```

### Using the Application
1. **Select Input File**: Click "Browse Files" to select an image file (SVG, PNG, JPG, etc.)
2. **Choose Conversion Type**: Select from the dropdown menu:
   - `svg_to_webp`: Convert SVG files to WebP format
   - `png_to_ico`: Convert PNG files to ICO format (with multiple sizes)
   - `svg_to_ico`: Convert SVG files to ICO format
3. **Choose Output Location**: Click "Choose Save Location" to specify where to save the converted file
4. **Convert**: Click "Convert Image" to start the conversion process

### Testing the Installation
Run the test script to verify everything is working:
```
python test_app.py
```

## Dependencies
The project requires the following Python packages:
- Ttkbootstrap
- Pillow
- svglib
- reportlab

Make sure to install these packages using the `requirements.txt` file.

## Contributing
Contributions are welcome! If you have suggestions or improvements, please open an issue or submit a pull request.

## License
This project is licensed under the MIT License. See the LICENSE file for more details.