import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from converter import ImageConverterApp

def main():
    """Main entry point for the Image Converter application."""
    # Create the main window using ttkbootstrap
    root = ttk.Window(themename="darkly")
    root.title("Image Converter")
    root.geometry("500x400")
    root.resizable(True, True)

    # Create and run the application
    app = ImageConverterApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()