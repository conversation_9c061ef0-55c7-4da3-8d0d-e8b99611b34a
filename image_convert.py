"""
Image conversion module for converting between different image formats.
Supports SVG to WebP, PNG to ICO, and SVG to ICO conversions.
"""

import os
from PIL import Image
import tempfile

try:
    import svglib.svglib as svglib
    from reportlab.graphics import renderPM
    SVG_SUPPORT = True
except ImportError:
    SVG_SUPPORT = False
    print("Warning: SVG support not available. Install svglib and reportlab for SVG conversions.")


def svg_to_webp(input_path, output_path, size=(512, 512)):
    """Convert SVG to WebP format."""
    if not SVG_SUPPORT:
        raise Exception("SVG conversion not supported. Please install svglib and reportlab.")

    try:
        # Create a temporary PNG file first
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            temp_png_path = temp_file.name

        # Convert SVG to PNG using svglib and reportlab
        drawing = svglib.svg2rlg(input_path)
        renderPM.drawToFile(drawing, temp_png_path, fmt='PNG')
        
        # Open the PNG and convert to WebP
        with Image.open(temp_png_path) as img:
            # Resize if needed
            img = img.resize(size, Image.Resampling.LANCZOS)
            # Convert to RGB if necessary (WebP doesn't support all modes)
            if img.mode in ('RGBA', 'LA'):
                # Create a white background
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'RGBA':
                    background.paste(img, mask=img.split()[-1])  # Use alpha channel as mask
                else:
                    background.paste(img)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Save as WebP
            img.save(output_path, 'WEBP', quality=90)
        
        # Clean up temporary file
        os.unlink(temp_png_path)
        return True
        
    except Exception as e:
        # Clean up temporary file if it exists
        if 'temp_png_path' in locals() and os.path.exists(temp_png_path):
            os.unlink(temp_png_path)
        raise Exception(f"Error converting SVG to WebP: {str(e)}")


def png_to_ico(input_path, output_path, sizes=None):
    """Convert PNG to ICO format with multiple sizes."""
    if sizes is None:
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
    
    try:
        with Image.open(input_path) as img:
            # Convert to RGBA if not already
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # Create multiple sizes for the ICO file
            icon_sizes = []
            for size in sizes:
                resized_img = img.resize(size, Image.Resampling.LANCZOS)
                icon_sizes.append(resized_img)
            
            # Save as ICO with multiple sizes
            icon_sizes[0].save(output_path, format='ICO', sizes=[img.size for img in icon_sizes])
        
        return True
        
    except Exception as e:
        raise Exception(f"Error converting PNG to ICO: {str(e)}")


def svg_to_ico(input_path, output_path, sizes=None):
    """Convert SVG to ICO format."""
    if not SVG_SUPPORT:
        raise Exception("SVG conversion not supported. Please install svglib and reportlab.")

    if sizes is None:
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]

    try:
        # Create a temporary PNG file first
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            temp_png_path = temp_file.name

        # Convert SVG to PNG using svglib and reportlab
        drawing = svglib.svg2rlg(input_path)
        renderPM.drawToFile(drawing, temp_png_path, fmt='PNG')
        
        # Now convert the PNG to ICO
        with Image.open(temp_png_path) as img:
            # Convert to RGBA if not already
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # Create multiple sizes for the ICO file
            icon_sizes = []
            for size in sizes:
                resized_img = img.resize(size, Image.Resampling.LANCZOS)
                icon_sizes.append(resized_img)
            
            # Save as ICO with multiple sizes
            icon_sizes[0].save(output_path, format='ICO', sizes=[img.size for img in icon_sizes])
        
        # Clean up temporary file
        os.unlink(temp_png_path)
        return True
        
    except Exception as e:
        # Clean up temporary file if it exists
        if 'temp_png_path' in locals() and os.path.exists(temp_png_path):
            os.unlink(temp_png_path)
        raise Exception(f"Error converting SVG to ICO: {str(e)}")


def image_convert(input_path, output_path, conversion_type):
    """
    Main conversion function that routes to the appropriate converter.
    
    Args:
        input_path (str): Path to the input image file
        output_path (str): Path where the converted image should be saved
        conversion_type (str): Type of conversion ('svg_to_webp', 'png_to_ico', 'svg_to_ico')
    
    Returns:
        bool: True if conversion was successful
        
    Raises:
        Exception: If conversion fails or unsupported conversion type
    """
    if not os.path.exists(input_path):
        raise Exception(f"Input file does not exist: {input_path}")
    
    # Ensure output directory exists
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    conversion_functions = {
        'svg_to_webp': svg_to_webp,
        'png_to_ico': png_to_ico,
        'svg_to_ico': svg_to_ico
    }
    
    if conversion_type not in conversion_functions:
        raise Exception(f"Unsupported conversion type: {conversion_type}")
    
    try:
        return conversion_functions[conversion_type](input_path, output_path)
    except Exception as e:
        raise Exception(f"Conversion failed: {str(e)}")


# Test function for debugging
if __name__ == "__main__":
    # Example usage
    print("Image conversion module loaded successfully!")
    print("Supported conversions: svg_to_webp, png_to_ico, svg_to_ico")
