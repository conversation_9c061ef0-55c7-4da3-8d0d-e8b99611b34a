import tkinter as tk
from tkinter import filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
from image_convert import image_convert

class ImageConverterApp:
    def __init__(self, master):
        self.master = master
        self.input_file = None
        self.output_file = None

        # Configure the main window
        self.master.title("Image Converter")

        self.create_widgets()

    def create_widgets(self):
        # Main container frame
        main_frame = ttk.Frame(self.master, padding="20")
        main_frame.pack(fill=BOTH, expand=True)

        # Title
        title_label = ttk.Label(
            main_frame,
            text="Image Converter",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # File selection section
        file_frame = ttk.LabelFrame(main_frame, text="Select Input File", padding="10")
        file_frame.pack(fill=X, pady=(0, 15))

        self.file_label = ttk.Label(
            file_frame,
            text="No file selected",
            foreground="gray",
            font=("Arial", 10)
        )
        self.file_label.pack(pady=(0, 10))

        # Browse button
        browse_button = ttk.Button(
            file_frame,
            text="Browse Files",
            command=self.browse_file,
            bootstyle="outline-primary"
        )
        browse_button.pack()

        # Conversion type selection
        conversion_frame = ttk.LabelFrame(main_frame, text="Conversion Type", padding="10")
        conversion_frame.pack(fill=X, pady=(0, 15))

        self.conversion_type = tk.StringVar()
        self.conversion_dropdown = ttk.Combobox(
            conversion_frame,
            textvariable=self.conversion_type,
            values=['svg_to_webp', 'png_to_ico', 'svg_to_ico'],
            state="readonly",
            width=30
        )
        self.conversion_dropdown.set('Select conversion type')
        self.conversion_dropdown.pack(pady=5)

        # Output file selection
        output_frame = ttk.LabelFrame(main_frame, text="Output Location", padding="10")
        output_frame.pack(fill=X, pady=(0, 15))

        self.output_label = ttk.Label(
            output_frame,
            text="No output location selected",
            foreground="gray",
            font=("Arial", 10)
        )
        self.output_label.pack(pady=(0, 10))

        save_button = ttk.Button(
            output_frame,
            text="Choose Save Location",
            command=self.save_as,
            bootstyle="outline-secondary"
        )
        save_button.pack()

        # Convert button
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=X, pady=(20, 0))

        self.convert_button = ttk.Button(
            button_frame,
            text="Convert Image",
            command=self.convert_image,
            bootstyle="success",
            width=20
        )
        self.convert_button.pack()

        # Status label
        self.status_label = ttk.Label(
            main_frame,
            text="Ready to convert images",
            font=("Arial", 9),
            foreground="gray"
        )
        self.status_label.pack(pady=(10, 0))

    def browse_file(self):
        """Open file dialog to select input file."""
        file_types = [
            ("Image files", "*.png *.jpg *.jpeg *.svg *.bmp *.gif *.tiff"),
            ("SVG files", "*.svg"),
            ("PNG files", "*.png"),
            ("All files", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="Select Image File",
            filetypes=file_types
        )

        if file_path:
            self.input_file = file_path
            # Display shortened filename
            filename = os.path.basename(file_path)
            if len(filename) > 40:
                filename = filename[:37] + "..."
            self.file_label.config(text=f"Selected: {filename}", foreground="black")
            self.status_label.config(text="Input file selected. Choose conversion type and output location.")

    def save_as(self):
        """Open save dialog to choose output location."""
        conversion_type = self.conversion_type.get()

        # Set default extension and file types based on conversion type
        if conversion_type == 'svg_to_webp':
            default_ext = ".webp"
            file_types = [("WebP files", "*.webp"), ("All files", "*.*")]
        elif conversion_type in ['png_to_ico', 'svg_to_ico']:
            default_ext = ".ico"
            file_types = [("ICO files", "*.ico"), ("All files", "*.*")]
        else:
            default_ext = ".webp"
            file_types = [
                ("WebP files", "*.webp"),
                ("ICO files", "*.ico"),
                ("All files", "*.*")
            ]

        self.output_file = filedialog.asksaveasfilename(
            title="Save Converted Image As",
            defaultextension=default_ext,
            filetypes=file_types
        )

        if self.output_file:
            # Display shortened filename
            filename = os.path.basename(self.output_file)
            if len(filename) > 40:
                filename = filename[:37] + "..."
            self.output_label.config(text=f"Save as: {filename}", foreground="black")
            self.status_label.config(text="Ready to convert. Click 'Convert Image' to start.")

    def convert_image(self):
        """Convert the selected image using the specified conversion type."""
        # Validate inputs
        if not self.input_file:
            messagebox.showerror("Error", "Please select an input file first.")
            return

        if not self.output_file:
            messagebox.showerror("Error", "Please choose an output location first.")
            return

        conversion_type = self.conversion_type.get()
        if not conversion_type or conversion_type == 'Select conversion type':
            messagebox.showerror("Error", "Please select a conversion type.")
            return

        # Validate file exists
        if not os.path.exists(self.input_file):
            messagebox.showerror("Error", "Input file no longer exists.")
            return

        try:
            # Update status
            self.status_label.config(text="Converting image... Please wait.")
            self.master.update()

            # Perform conversion
            image_convert(self.input_file, self.output_file, conversion_type)

            # Success message
            self.status_label.config(text="Conversion completed successfully!")
            messagebox.showinfo(
                "Success",
                f"Image converted successfully!\nSaved to: {self.output_file}"
            )

        except Exception as e:
            self.status_label.config(text="Conversion failed.")
            messagebox.showerror("Conversion Error", f"Failed to convert image:\n{str(e)}")

if __name__ == "__main__":
    # Create the main window using ttkbootstrap
    root = ttk.Window(themename="darkly")
    root.title("Image Converter")
    root.geometry("500x400")
    root.resizable(True, True)

    # Create and run the application
    app = ImageConverterApp(root)
    root.mainloop()