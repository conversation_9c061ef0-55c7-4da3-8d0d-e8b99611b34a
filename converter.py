import tkinter as tk
from tkinter import filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
from image_convert import image_convert

class ImageConverterApp:
    def __init__(self, master):
        self.master = master
        self.input_file = None
        self.output_file = None

        # Configure the main window
        self.master.title("Image Converter")

        self.create_widgets()

    def create_widgets(self):
        # Main container frame
        main_frame = ttk.Frame(self.master, padding="20")
        main_frame.pack(fill=BOTH, expand=True)

        # Title
        title_label = ttk.Label(
            main_frame,
            text="Image Converter",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # File selection section
        file_frame = ttk.LabelFrame(main_frame, text="Select Input File", padding="10")
        file_frame.pack(fill=X, pady=(0, 15))

        self.file_label = ttk.Label(
            file_frame,
            text="No file selected",
            foreground="gray",
            font=("Arial", 10)
        )
        self.file_label.pack(pady=(0, 10))

        # Browse button
        browse_button = ttk.Button(
            file_frame,
            text="Browse Files",
            command=self.browse_file,
            bootstyle="outline-primary"
        )
        browse_button.pack()

        # Conversion type selection
        conversion_frame = ttk.LabelFrame(main_frame, text="Conversion Type", padding="10")
        conversion_frame.pack(fill=X, pady=(0, 15))

        self.conversion_type = tk.StringVar()
        self.conversion_dropdown = ttk.Combobox(
            conversion_frame,
            textvariable=self.conversion_type,
            values=['svg_to_webp', 'png_to_ico', 'svg_to_ico'],
            state="readonly",
            width=30
        )
        self.conversion_dropdown.set('Select conversion type')
        self.conversion_dropdown.pack(pady=5)

        # Convert button (integrated with output selection)
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=X, pady=(20, 0))

        self.convert_button = ttk.Button(
            button_frame,
            text="Convert & Save Image",
            command=self.convert_and_save_image,
            bootstyle="success",
            width=25
        )
        self.convert_button.pack()

        # Optional: Add a separate "Choose Location" button for advanced users
        advanced_frame = ttk.Frame(main_frame)
        advanced_frame.pack(fill=X, pady=(10, 0))

        self.advanced_button = ttk.Button(
            advanced_frame,
            text="Advanced: Pre-select Save Location",
            command=self.save_as,
            bootstyle="outline-secondary",
            width=30
        )
        self.advanced_button.pack()

        self.output_label = ttk.Label(
            advanced_frame,
            text="",
            foreground="gray",
            font=("Arial", 9)
        )
        self.output_label.pack(pady=(5, 0))

        # Status label
        self.status_label = ttk.Label(
            main_frame,
            text="Ready to convert images",
            font=("Arial", 9),
            foreground="gray"
        )
        self.status_label.pack(pady=(10, 0))

    def browse_file(self):
        """Open file dialog to select input file."""
        file_types = [
            ("Image files", "*.png *.jpg *.jpeg *.svg *.bmp *.gif *.tiff"),
            ("SVG files", "*.svg"),
            ("PNG files", "*.png"),
            ("All files", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="Select Image File",
            filetypes=file_types
        )

        if file_path:
            self.input_file = file_path
            # Display shortened filename
            filename = os.path.basename(file_path)
            if len(filename) > 40:
                filename = filename[:37] + "..."
            self.file_label.config(text=f"Selected: {filename}", foreground="black")
            self.status_label.config(text="Input file selected. Choose conversion type and click 'Convert & Save Image'.")

    def save_as(self):
        """Advanced option: Pre-select output location."""
        conversion_type = self.conversion_type.get()

        if not conversion_type or conversion_type == 'Select conversion type':
            messagebox.showwarning(
                "Select Conversion Type",
                "Please select a conversion type first to determine the output format."
            )
            return

        self.output_file = self._get_save_location(conversion_type)

        if self.output_file:
            # Display shortened filename
            filename = os.path.basename(self.output_file)
            if len(filename) > 50:
                filename = filename[:47] + "..."
            self.output_label.config(
                text=f"Pre-selected: {filename}",
                foreground="darkgreen"
            )
            self.status_label.config(
                text="Output location pre-selected. Click 'Convert & Save Image' to proceed."
            )

    def convert_and_save_image(self):
        """Convert the image and prompt for save location in one step."""
        # Validate inputs
        if not self.input_file:
            messagebox.showerror("Error", "Please select an input file first.")
            return

        conversion_type = self.conversion_type.get()
        if not conversion_type or conversion_type == 'Select conversion type':
            messagebox.showerror("Error", "Please select a conversion type.")
            return

        # Validate file exists
        if not os.path.exists(self.input_file):
            messagebox.showerror("Error", "Input file no longer exists.")
            return

        # If no output file is pre-selected, prompt for save location
        if not self.output_file:
            self.output_file = self._get_save_location(conversion_type)
            if not self.output_file:
                return  # User cancelled the save dialog

        try:
            # Update status
            self.status_label.config(text="Converting image... Please wait.")
            self.master.update()

            # Perform conversion
            image_convert(self.input_file, self.output_file, conversion_type)

            # Success message
            self.status_label.config(text="Conversion completed successfully!")
            messagebox.showinfo(
                "Success",
                f"Image converted successfully!\nSaved to: {self.output_file}"
            )

            # Reset for next conversion
            self.output_file = None
            self.output_label.config(text="", foreground="gray")

        except Exception as e:
            self.status_label.config(text="Conversion failed.")
            messagebox.showerror("Conversion Error", f"Failed to convert image:\n{str(e)}")

    def _get_save_location(self, conversion_type):
        """Helper method to get save location based on conversion type."""
        # Set default extension and file types based on conversion type
        if conversion_type == 'svg_to_webp':
            default_ext = ".webp"
            file_types = [("WebP files", "*.webp"), ("All files", "*.*")]
        elif conversion_type in ['png_to_ico', 'svg_to_ico']:
            default_ext = ".ico"
            file_types = [("ICO files", "*.ico"), ("All files", "*.*")]
        else:
            default_ext = ".webp"
            file_types = [
                ("WebP files", "*.webp"),
                ("ICO files", "*.ico"),
                ("All files", "*.*")
            ]

        # Generate suggested filename based on input file
        if self.input_file:
            input_name = os.path.splitext(os.path.basename(self.input_file))[0]
            suggested_name = f"{input_name}_converted{default_ext}"
        else:
            suggested_name = f"converted_image{default_ext}"

        return filedialog.asksaveasfilename(
            title="Save Converted Image As",
            defaultextension=default_ext,
            initialfile=suggested_name,
            filetypes=file_types
        )

    def convert_image(self):
        """Legacy method - redirects to the new integrated method."""
        self.convert_and_save_image()

if __name__ == "__main__":
    # Create the main window using ttkbootstrap
    root = ttk.Window(themename="darkly")
    root.title("Image Converter")
    root.geometry("500x400")
    root.resizable(True, True)

    # Create and run the application
    app = ImageConverterApp(root)
    root.mainloop()